# UI框架遷移計劃：從自建系統遷移到Skeleton UI

最後更新: 2025/7/23

---

## 專案背景

### 遷移理由
- **預防技術債務**：避免自建UI系統維護複雜度指數級增長
- **未來擴展準備**：為多語系支援、新功能開發奠定基礎
- **最佳遷移時機**：當前專案規模小，遷移成本可控
- **長期效益**：預計年度維護成本節省73%

### 當前專案規模
- 總頁面數：6個頁面
- 自建UI使用：27處自定義類別使用
- 總代碼量：1,797行路由代碼
- 自建CSS類別：35個
- 主題管理代碼：268行

---

## 遷移實施計劃

### 第一階段：基礎架構遷移

#### Step 1. 環境準備
- [ ] 安裝Skeleton相關套件
  ```bash
  pnpm add -D @skeletonlabs/skeleton @skeletonlabs/skeleton-svelte
  ```
- [ ] 記錄當前所有自定義CSS類別使用位置
- [ ] 建立遷移追蹤檔案

#### Step 2. 核心配置修改
- [ ] 修改 `src/app.css`
  - [ ] 移除現有自定義CSS類別定義（35個類別）
  - [ ] 導入Skeleton主題
    ```css
    @import '@skeletonlabs/skeleton/themes/theme-cerberus.css';
    @import '@skeletonlabs/skeleton/styles/all.css';
    ```
  - [ ] 保留必要的全域樣式
- [ ] 修改 `src/app.html`
  - [ ] 在html標籤添加 `data-theme="cerberus"`
  - [ ] 移除舊的防閃爍腳本引用
- [ ] 配置 `tailwind.config.js`
  - [ ] 添加Skeleton plugin配置
  - [ ] 驗證色彩系統相容性
- [ ] 測試基礎建置是否成功

#### Step 3. 主題系統遷移
- [ ] 移除現有主題管理檔案
  - [ ] 刪除 `src/lib/themeStore.ts` (98行)
  - [ ] 刪除 `src/lib/utils/themeUtils.ts` (104行)
  - [ ] 刪除 `static/themeScript.js` (66行)
- [ ] 實作Skeleton主題切換系統
  - [ ] 研讀Skeleton主題切換文檔
  - [ ] 建立新的主題管理元件
  - [ ] 實作防閃爍機制
  - [ ] 確保localStorage持久化功能
- [ ] 驗證主題切換功能正常

### 第二階段：元件庫遷移

#### Step 4. 基礎元件重構
- [ ] 重構 `src/lib/components/Navigation.svelte` (146行)
  - [ ] 使用Skeleton `<AppBar>` 元件
  - [ ] 整合Skeleton主題切換元件
  - [ ] 保持原有的導航邏輯
  - [ ] 確保響應式設計正常
  - [ ] 測試使用者選單功能
- [ ] 重構 `src/lib/components/LoadingState.svelte` (92行)
  - [ ] 使用Skeleton內建loading元件
  - [ ] 替換自建skeleton動畫
  - [ ] 保持原有的狀態管理邏輯
  - [ ] 測試各種載入狀態顯示

#### Step 5. 登入頁面重構
- [ ] 重構 `src/routes/login/+page.svelte` (261行)
  - [ ] 替換所有 `.btn` → Skeleton Button元件
  - [ ] 替換所有 `.input` → Skeleton Input元件
  - [ ] 替換所有 `.card` → Skeleton Card元件
  - [ ] 替換所有 `.alert` → Skeleton Alert元件
  - [ ] 確保表單驗證功能正常
  - [ ] 測試登入流程完整性
  - [ ] 驗證錯誤訊息顯示

#### Step 6. 藥物管理頁面重構
- [ ] 重構 `src/routes/pharmaceuticals/+page.svelte` (152行)
  - [ ] 使用Skeleton Table元件
  - [ ] 使用Skeleton Button元件
  - [ ] 保持原有的資料載入邏輯
  - [ ] 測試CRUD操作功能
- [ ] 重構 `src/routes/pharmaceuticals/new/+page.svelte` (252行)
  - [ ] 使用Skeleton Form元件
  - [ ] 替換所有表單控制項
  - [ ] 保持原有的驗證邏輯
  - [ ] 測試表單提交功能
  - [ ] 驗證錯誤處理機制

#### Step 7. 主頁面重構 (複雜頁面)
- [ ] 重構 `src/routes/whendiditake/+page.svelte` (596行)
  - [ ] 分析現有複雜交互邏輯
  - [ ] 使用Skeleton元件替換自定義UI
  - [ ] 保持原有的狀態管理
  - [ ] 確保QuickTakeOption元件整合
  - [ ] 確保TakeRecord元件整合
  - [ ] 測試完整的服藥記錄流程
- [ ] 重構頁面內元件
  - [ ] `src/routes/whendiditake/components/QuickTakeOption.svelte` (269行)
  - [ ] `src/routes/whendiditake/components/TakeRecord.svelte` (145行)

#### Step 8. 佈局和首頁重構
- [ ] 重構 `src/routes/+layout.svelte` (103行)
  - [ ] 整合新的主題系統
  - [ ] 使用Skeleton佈局元件
  - [ ] 保持原有的認證邏輯
  - [ ] 測試全域佈局功能
- [ ] 重構 `src/routes/+page.svelte` (19行)
  - [ ] 簡單頁面，快速遷移
  - [ ] 確保重新導向功能正常

### 第三階段：品質保證與優化

#### Step 9. 樣式清理與統一
- [ ] 完整移除app.css中舊的自定義類別
- [ ] 統一色彩系統使用Skeleton色盤
- [ ] 檢查所有頁面的視覺一致性
- [ ] 驗證響應式設計在各裝置上的表現
- [ ] 確保無遺留的variant-filled-*類別

#### Step 10. 功能完整性測試
- [ ] 登入/登出功能測試
  - [ ] 正常登入流程
  - [ ] 錯誤處理顯示
  - [ ] JWT過期處理
- [ ] 主題切換功能測試
  - [ ] Light/Dark/Auto模式切換
  - [ ] localStorage持久化
  - [ ] 防閃爍機制
  - [ ] 系統主題偵測
- [ ] 頁面導航測試
  - [ ] 所有路由正常運作
  - [ ] 導航高亮顯示正確
  - [ ] 響應式導航選單

#### Step 11. 核心業務功能測試
- [ ] 藥物管理功能
  - [ ] 新增藥物
  - [ ] 編輯藥物資訊
  - [ ] 刪除藥物
  - [ ] 藥物列表顯示
- [ ] 服藥記錄功能
  - [ ] 快速記錄服藥
  - [ ] 查看歷史記錄
  - [ ] 記錄編輯/刪除
- [ ] 資料載入狀態
  - [ ] Loading動畫顯示
  - [ ] 錯誤狀態處理
  - [ ] 空資料狀態


---

## 風險控制檢查表

### 技術風險控制
- [ ] 建立完整的Git分支保護
- [ ] 每個Step完成後立即提交
- [ ] 保持可回滾的檢查點
- [ ] 建立詳細的遷移異動日誌
- [ ] 準備回滾程序文件

### 功能風險控制
- [ ] 驗證所有現有功能正常運作
- [ ] 確保資料完整性不受影響
- [ ] 測試所有使用者流程
- [ ] 驗證GraphQL API整合無異常
- [ ] 確認認證系統穩定運作

### 進度風險控制
- [ ] 建立每個Step的完成標準
- [ ] 設定問題上報機制
- [ ] 準備資源調配計劃
- [ ] 建立品質閘門檢查點

---

## 驗收標準

### 功能性驗收
- [ ] 所有原有功能正常運作
- [ ] 主題切換功能完整實作
- [ ] 響應式設計完全相容
- [ ] 無任何功能退化

### 非功能性驗收
- [ ] 頁面載入效能不劣化
- [ ] 主題切換動畫流暢
- [ ] 跨瀏覽器相容性良好
- [ ] 無障礙功能符合標準

### 代碼品質驗收
- [ ] 代碼總量減少30%以上
- [ ] 主題管理代碼減少80%以上
- [ ] 移除所有自定義CSS類別
- [ ] TypeScript編譯無錯誤

### 維護性驗收
- [ ] UI元件使用標準化
- [ ] 設計系統一致性100%
- [ ] 新功能開發流程簡化
- [ ] 文檔更新完整

---

## 備註

此遷移計劃遵循CollaborationGuild.md的開發原則，優先使用成熟的高階封裝工具，避免重複造輪子。遷移完成後將大幅提升專案的可維護性和可擴展性，為未來的功能發展奠定堅實基礎。