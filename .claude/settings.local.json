{"permissions": {"allow": ["<PERSON><PERSON>(python:*)", "Bash(pnpm dev:*)", "Bash(pnpm run:*)", "Bash(kill:*)", "<PERSON><PERSON>(curl:*)", "Bash(pnpm approve-builds:*)", "<PERSON><PERSON>(wget:*)", "Bash(pnpm:*)", "Bash(lsof:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "WebFetch(domain:localhost)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(node:*)", "Bash(npx tailwindcss:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(npm run build:*)", "Bash(ps:*)", "Bash(./dev-server.sh:*)", "<PERSON><PERSON>(source:*)", "Bash(get_server_pids \"frontend\")", "Bash(pgrep:*)", "Bash(Misc/dev-server.sh check:*)", "Bash(npm run check:*)", "<PERSON><PERSON>(echo:*)", "Bash(sqlite3:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(npm run typecheck:*)", "mcp__ide__executeCode", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "WebFetch(domain:github.com)", "WebFetch(domain:www.npmjs.com)", "WebFetch(domain:www.skeleton.dev)"], "deny": []}}