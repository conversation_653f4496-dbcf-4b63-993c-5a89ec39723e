<!--
  載入狀態組件
  最後更新: 2025/7/21
-->
<script lang="ts">
  import { LoadingState, shouldShowLoader, shouldShowSkeleton, hasData, hasError } from '$lib/utils/loadingHelpers';
  
  export let state: LoadingState;
  export let error: string | undefined = undefined;
  export let showRetry: boolean = true;
  export let retryCallback: (() => void) | undefined = undefined;
  export let skeletonType: 'list' | 'card' | 'table' = 'list';
  export let itemCount: number = 5;
</script>

{#if shouldShowSkeleton(state)}
  <!-- 骨架屏 -->
  <div class="animate-pulse space-y-4">
    {#if skeletonType === 'list'}
      {#each Array(itemCount) as _, i}
        <div class="flex items-center space-x-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <div class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
            <div class="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
          <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
        </div>
      {/each}
    {:else if skeletonType === 'card'}
      {#each Array(itemCount) as _, i}
        <div class="p-6 bg-gray-100 dark:bg-gray-800 rounded-lg space-y-4">
          <div class="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
          <div class="space-y-2">
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-5/6"></div>
          </div>
        </div>
      {/each}
    {:else if skeletonType === 'table'}
      <div class="space-y-2">
        {#each Array(itemCount) as _, i}
          <div class="flex space-x-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/6"></div>
            <div class="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/5"></div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
{:else if shouldShowLoader(state)}
  <!-- 載入中指示器 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="relative">
      <div class="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
    </div>
    <p class="text-sm text-gray-600 dark:text-gray-300">載入中...</p>
  </div>
{:else if hasError(state)}
  <!-- 錯誤狀態 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="flex items-center space-x-2 text-red-600 dark:text-red-400">
      <iconify-icon icon="lucide:alert-circle" class="text-2xl"></iconify-icon>
      <span class="text-lg font-medium">載入失敗</span>
    </div>
    
    {#if error}
      <p class="text-sm text-gray-600 dark:text-gray-300 text-center max-w-md">
        {error}
      </p>
    {/if}
    
    {#if showRetry && retryCallback}
      <button
        class="btn variant-filled-primary"
        on:click={retryCallback}
      >
        <iconify-icon icon="lucide:refresh-cw" class="text-lg mr-2"></iconify-icon>
        重試
      </button>
    {/if}
  </div>
{:else if state === LoadingState.NOT_LOADED}
  <!-- 尚未載入狀態 -->
  <div class="flex flex-col items-center justify-center py-12 space-y-4">
    <div class="text-gray-400 dark:text-gray-500">
      <iconify-icon icon="lucide:database" class="text-4xl"></iconify-icon>
    </div>
    <p class="text-sm text-gray-600 dark:text-gray-300">等待載入資料</p>
  </div>
{/if}