<script lang="ts">
  import type { GraphQLTakeType } from '$lib/graphql/generated';
  import { createEventDispatcher } from 'svelte';
  import { takeRecordsStore } from '$lib/stores/dataStore';
  import { fly } from 'svelte/transition';
  import { flip } from 'svelte/animate';
  import { format, isSameDay, parseISO } from 'date-fns';
  import { zhTW } from 'date-fns/locale';

  export let record: GraphQLTakeType & { pharmaceuticalName?: string; dosage?: number; dosageUnit?: string; };
  export let expandable: boolean = true;
  export let isDeleting: boolean = false;
  
  let isExpanded = false;
  let swipeElement: HTMLElement;
  let swipeOffset = 0;
  let isSwipeActive = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let isDragging = false;

  const dispatch = createEventDispatcher<{
    edit: GraphQLTakeType;
    delete: GraphQLTakeType;
  }>();

  // 計算當日累積服用總劑量
  $: dailyTotalDosage = ($takeRecordsStore.data || [])
    .filter(r => {
      const d1 = typeof r.takenAt === 'string' ? parseISO(r.takenAt) : r.takenAt;
      const d2 = typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt;
      return isSameDay(d1, d2) && r.pharmaceuticalId === record.pharmaceuticalId;
    })
    .reduce((total, r) => total + r.quantity, 0);

  function toggleExpand() {
    if (expandable) {
      isExpanded = !isExpanded;
    }
  }

  function handleEdit() {
    // 更新使用者活動時間
    if (typeof window !== 'undefined') {
      import('$lib/auth').then(({ updateUserActivity }) => {
        updateUserActivity();
      }).catch(err => console.warn('無法更新使用者活動時間:', err));
    }
    dispatch('edit', record);
  }

  function handleDelete() {
    // 更新使用者活動時間
    if (typeof window !== 'undefined') {
      import('$lib/auth').then(({ updateUserActivity }) => {
        updateUserActivity();
      }).catch(err => console.warn('無法更新使用者活動時間:', err));
    }
    dispatch('delete', record);
  }
</script>

<div 
  class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200 {isDeleting ? 'opacity-50' : ''}"
  out:fly={{ y: -20, duration: 300 }}
>
  <!-- 主要資訊 (橫向顯示) -->
  <div 
    class="flex items-center justify-between py-3 px-4 {expandable ? 'cursor-pointer' : ''}"
    on:click={toggleExpand}
    role={expandable ? 'button' : 'none'}
    tabindex={expandable ? 0 : -1}
    on:keydown={(e) => e.key === 'Enter' && toggleExpand()}
  >
    <div class="flex items-center space-x-3 flex-1">
      <!-- 藥物名稱 -->
      <div class="flex-1 min-w-0">
        <h3 class="font-medium text-gray-900 dark:text-gray-100 truncate">
          {record.pharmaceutical?.name}
        </h3>
      </div>
      
      <!-- 劑量 -->
      <div class="text-right">
        <span class="font-medium text-blue-600 dark:text-blue-400">
          {record.quantity} {record.pharmaceutical?.dosageUnit}
        </span>
      </div>
      
      <!-- 時間 -->
      <div class="text-right text-sm text-gray-500 dark:text-gray-400 min-w-0">
        <div class="truncate">{format(typeof record.takenAt === 'string' ? parseISO(record.takenAt) : record.takenAt, 'HH:mm', { locale: zhTW })}</div>
      </div>
    </div>

    <!-- 展開按鈕 -->
    {#if expandable}
      <div class="ml-3 text-gray-400 hover:text-gray-600 transition-transform duration-200 {isExpanded ? 'rotate-180' : ''}">
        <iconify-icon icon="lucide:chevron-down" class="text-lg"></iconify-icon>
      </div>
    {/if}
  </div>

  <!-- 展開內容 -->
  {#if isExpanded}
    <div class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-4 py-3 space-y-2">
      <!-- 註記 -->
      {#if record.notes}
        <div class="flex items-start space-x-2">
          <iconify-icon icon="lucide:sticky-note" class="text-sm text-gray-500 mt-0.5"></iconify-icon>
          <div class="text-sm text-gray-600 dark:text-gray-300">
            <span class="font-medium">註記：</span>
            <span>{record.notes}</span>
          </div>
        </div>
      {/if}

      <!-- 當日累積總劑量 -->
      <div class="flex items-center space-x-2">
        <iconify-icon icon="lucide:activity" class="text-sm text-blue-500"></iconify-icon>
        <span class="text-sm text-gray-600 dark:text-gray-300">
          當日累積：<span class="font-semibold text-blue-600 dark:text-blue-400">{dailyTotalDosage} {record.pharmaceutical?.dosageUnit}</span>
        </span>
      </div>

      <!-- 操作按鈕 -->
      <div class="flex justify-end space-x-2 pt-1">
        <button 
          class="test-btn !bg-gray-600 hover:!bg-gray-700 !px-3 !py-1 !text-sm"
          on:click|stopPropagation={handleEdit}
        >
          <iconify-icon icon="lucide:edit-2" class="text-sm"></iconify-icon>
          <span>編輯</span>
        </button>
        <button 
          class="test-btn !bg-red-600 hover:!bg-red-700 !px-3 !py-1 !text-sm"
          on:click|stopPropagation={handleDelete}
        >
          <iconify-icon icon="lucide:trash-2" class="text-sm"></iconify-icon>
          <span>刪除</span>
        </button>
      </div>
    </div>
  {/if}
</div>