<script>
  import { goto } from '$app/navigation';
  import { createPharmaceutical } from '$lib/graphql/services';
  import { PHARMACEUTICAL_FORMULATIONS, COMMON_DOSAGE_UNITS } from '$lib/constants/pharmaceutical';
  import Navigation from '$lib/components/Navigation.svelte';

  let form = {
    name: '',
    formulationType: 'PILL',
    dosagePerServing: '',
    dosageUnit: '',
    servingUnitName: '',
    currentPlan: true,
    description: ''
  };

  let loading = false;
  let errors = {};

  function validateForm() {
    errors = {};
    
    if (!form.name.trim()) {
      errors.name = '藥物名稱為必填';
    }
    
    if (!form.dosagePerServing || isNaN(parseFloat(form.dosagePerServing))) {
      errors.dosagePerServing = '每份劑量必須為有效數字';
    }
    
    if (!form.dosageUnit.trim()) {
      errors.dosageUnit = '劑量單位為必填';
    }
    
    return Object.keys(errors).length === 0;
  }

  async function handleSubmit() {
    if (!validateForm()) {
      return;
    }

    try {
      loading = true;
      
      const pharmaceuticalInput = {
        name: form.name.trim(),
        formulationType: form.formulationType,
        dosagePerServing: parseFloat(form.dosagePerServing),
        dosageUnit: form.dosageUnit.trim(),
        servingUnitName: form.servingUnitName.trim() || null,
        currentPlan: form.currentPlan,
        description: form.description.trim() || null
      };

      await createPharmaceutical({ pharmaceutical: pharmaceuticalInput });

      // 成功建立後跳轉到藥物清單頁面
      goto('/pharmaceuticals');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知錯誤';
      alert(`建立失敗：${errorMessage}`);
      console.error('建立藥物失敗:', err);
    } finally {
      loading = false;
    }
  }

  function goBack() {
    goto('/pharmaceuticals');
  }
</script>

<div class="min-h-screen bg-surface-50 dark:bg-surface-900">
  <Navigation />

  <div class="container mx-auto px-4 pt-20 pb-4 max-w-2xl">
  <div class="flex items-center mb-6">
    <button class="btn btn-ghost text-sm mr-4" on:click={goBack}>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
      返回
    </button>
    <h1 class="text-3xl font-bold">新增藥物</h1>
  </div>

  <div class="card">
    <div class="p-6">
      <form on:submit|preventDefault={handleSubmit}>
        <div class="space-y-4">
          <!-- 藥物名稱 -->
          <div class="mb-4">
            <label class="label" for="name">
              <span class="label-text">藥物名稱 <span class="text-error-500">*</span></span>
            </label>
            <input 
              type="text" 
              id="name"
              bind:value={form.name}
              class="input {errors.name ? 'border-error-500' : ''}"
              placeholder="例如：普拿疼"
              required
            />
            {#if errors.name}
              <label class="label">
                <span class="text-sm text-error-600">{errors.name}</span>
              </label>
            {/if}
          </div>

          <!-- 劑型 -->
          <div class="mb-4">
            <label class="label" for="formulationType">
              <span class="label-text">劑型</span>
            </label>
            <select 
              id="formulationType"
              bind:value={form.formulationType}
              class="input"
            >
              {#each PHARMACEUTICAL_FORMULATIONS as formulation}
                <option value={formulation.value}>{formulation.label}</option>
              {/each}
            </select>
          </div>

          <!-- 劑量和單位 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
              <label class="label" for="dosagePerServing">
                <span class="label-text">每份劑量 <span class="text-error-500">*</span></span>
              </label>
              <input 
                type="number" 
                id="dosagePerServing"
                bind:value={form.dosagePerServing}
                class="input {errors.dosagePerServing ? 'border-error-500' : ''}"
                placeholder="500"
                step="0.01"
                min="0"
                required
              />
              {#if errors.dosagePerServing}
                <label class="label">
                  <span class="text-sm text-error-600">{errors.dosagePerServing}</span>
                </label>
              {/if}
            </div>

            <div class="mb-4">
              <label class="label" for="dosageUnit">
                <span class="label-text">劑量單位 <span class="text-error-500">*</span></span>
              </label>
              <input 
                type="text" 
                id="dosageUnit"
                bind:value={form.dosageUnit}
                class="input {errors.dosageUnit ? 'border-error-500' : ''}"
                placeholder="mg"
                list="units"
                required
              />
              <datalist id="units">
                {#each COMMON_DOSAGE_UNITS as unit}
                  <option value={unit}></option>
                {/each}
              </datalist>
              {#if errors.dosageUnit}
                <label class="label">
                  <span class="text-sm text-error-600">{errors.dosageUnit}</span>
                </label>
              {/if}
            </div>
          </div>

          <!-- 新增欄位 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
              <label class="label" for="servingUnitName">
                <span class="label-text">數量單位</span>
              </label>
              <input 
                type="text" 
                id="servingUnitName"
                bind:value={form.servingUnitName}
                class="input"
                placeholder="顆、錠、包"
                maxlength="10"
              />
            </div>

            <div class="mb-4">
              <label class="label" for="currentPlan">
                <span class="label-text">治療中</span>
              </label>
              <div class="form-control">
                <label class="label cursor-pointer">
                  <span class="label-text">是否為目前治療中的藥物</span>
                  <input 
                    type="checkbox" 
                    id="currentPlan"
                    bind:checked={form.currentPlan}
                    class="checkbox"
                  />
                </label>
              </div>
            </div>
          </div>

          <!-- 描述 -->
          <div class="mb-4">
            <label class="label" for="description">
              <span class="label-text">描述</span>
            </label>
            <textarea 
              id="description"
              bind:value={form.description}
              class="input min-h-[100px] resize-y h-24"
              placeholder="例如：解熱鎮痛藥，用於感冒、頭痛等症狀"
            ></textarea>
          </div>
        </div>

        <div class="flex justify-end gap-3 mt-6">
          <button 
            type="button" 
            class="btn btn-ghost"
            on:click={goBack}
          >
            取消
          </button>
          <button 
            type="submit" 
            class="btn btn-primary"
            disabled={loading}
          >
            {#if loading}
              <svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              建立中...
            {:else}
              建立藥物
            {/if}
          </button>
        </div>
      </form>
    </div>
  </div>
  </div>
</div>